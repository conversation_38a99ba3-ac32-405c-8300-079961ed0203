<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跑酷游戏</title>
    <style>
        *{
            margin: 0;
            padding: 0;
            border: none;
            box-sizing: border-box;
        }
        body {
            margin: 0;
            padding: 0;
            background: #ede8d9 url(img/bj.jpg) no-repeat center center/100% auto;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        canvas {
            display: block;
        }
        #gameInfo {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 1000;
        }

        #countdown {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            font-size: 72px !important;
            color: white !important;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
            z-index: 1001 !important;
            font-weight: bold !important;
            font-family: Arial, sans-serif !important;
        }

        #gameOver {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            text-align: center !important;
            color: white !important;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
            z-index: 1001 !important;
            font-family: Arial, sans-serif !important;
            background: rgba(0, 0, 0, 0.7) !important;
            padding: 30px !important;
            border-radius: 10px !important;
        }

        #gameOver h1 {
            font-size: 48px !important;
            margin: 0 0 20px 0 !important;
            color: #FF6B6B !important;
        }

        #gameOver p {
            font-size: 24px !important;
            margin: 10px 0 !important;
        }

        #difficultyPanel {
            margin-top: 8px;
            font-size: 14px;
        }
        #difficultyPanel input[type="range"] {
            width: 200px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div id="gameInfo">
        <div>金币: <span id="coins">0</span></div>
        <div>距离: <span id="distance">0</span>m</div>
        <div>FPS: <span id="fps">0</span></div>
        <div id="difficultyPanel">
            难度: <span id="difficultyValue">1.0</span>
            <input id="difficultySlider" type="range" min="0.2" max="2" step="0.1" value="1">
        </div>
    </div>

    <div id="countdown" style="display: none;"></div>

    <div id="gameOver" style="display: none;">
        <h1>游戏结束!</h1>
        <p>最终得分: <span id="finalScore">0</span></p>
        <p>跑了 <span id="finalDistance">0</span>米</p>
        <p>点击/触摸屏幕重新开始</p>
    </div>

    <!-- 引入Three.js -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/three.min.js"></script>
    
    <script>
        // 游戏变量
        let scene, camera, renderer;
        let player, road, gameObjects = [];
        let sceneryObjects = [];
        let gameState = 'countdown'; // countdown, playing, gameOver
        let countdownTimer = 3;
        let playerSpeed = 3; // 玩家前进速度（米/秒），与FPS无关
        let score = 0;
        let distance = 0;
        let keys = {};
        const DEBUG_LOG = false;
        let clock = new THREE.Clock();
        let accumulator = 0;
        const FIXED_DT = 1 / 60;
        let backgroundPlaneWidth = 15; // 默认背景宽度

        // 玩家状态
        let playerLane = 1; // 0=左, 1=中, 2=右
        let targetLane = 1;
        let playerZ = 0;

        // 难度与障碍间距控制
        // difficulty: 0.2(简单) - 2(困难)。影响障碍生成概率与最小Z间距
        let difficulty = 1.0;
        const obstacleSpawnBaseProb = 0.10; // 基础障碍生成概率
        const baseObstacleGap = 60; // 基础最小Z间距（米）
        let lastObstacleZ = -Infinity; // 最近一次障碍的Z
        const baseCoinGap = 12; // 金币最小Z间距（米）
        let lastCoinZ = -Infinity; // 最近一次金币的Z
        const MAX_COINS = 60; // 活跃金币上限
        const MAX_OBSTACLES = 40; // 活跃障碍上限
        let lastSpawnZ = 0; // 距离驱动的生成节流

        // 共享几何与材质，减少GC与内存占用
        const shared = {
            coinGeometry: new THREE.CylinderGeometry(0.5, 0.5, 0.2, 8),
            coinMaterial: new THREE.MeshLambertMaterial({ color: 0xFFD700 }),
            obstacleGeometry: new THREE.BoxGeometry(1.5, 1.5, 1.5),
            obstacleMaterial: new THREE.MeshLambertMaterial({ color: 0x555555 }),
            treeTrunkGeometry: new THREE.CylinderGeometry(0.3, 0.4, 3, 8),
            treeTrunkMaterial: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
            treeLeavesGeometry: new THREE.SphereGeometry(1.5, 8, 8),
            treeLeavesMaterial: new THREE.MeshLambertMaterial({ color: 0x228B22 }),
            houseBodyGeometry: new THREE.BoxGeometry(3, 3, 3),
            houseBodyMaterial: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
            houseRoofGeometry: new THREE.ConeGeometry(2.2, 1.5, 4),
            houseRoofMaterial: new THREE.MeshLambertMaterial({ color: 0xDC143C }),
            signPoleGeometry: new THREE.CylinderGeometry(0.1, 0.1, 3, 8),
            signPoleMaterial: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
            signBoardGeometry: new THREE.BoxGeometry(2, 1.5, 0.2),
            signBoardMaterial: new THREE.MeshLambertMaterial({ color: 0x4169E1 })
        };
        const tempLookAt = new THREE.Vector3();

        // 笔直对齐的路边物体参数与追踪
        // 跑道宽2 => 边缘X在 ±1。房子宽3(半宽1.5)，树冠半径约1.5
        const leftHouseX = -1 - 1.5;  // 房子紧贴左边缘
        const rightTreeX = 1 + 1.5;   // 树紧贴右边缘
        let lastHouseZ = 200;         // 初始化后将会更新
        let lastTreeZ = 200;          // 初始化后将会更新

        // 初始化Three.js场景
        function initThreeJS() {
            // 创建场景
            scene = new THREE.Scene();
            // scene.fog = new THREE.Fog(0xffffff, 50, 200); // 移除雾效

            // 创建摄像机 (透视摄像机) - 调整视野角度避免玩家变小
            camera = new THREE.PerspectiveCamera(90, window.innerWidth / window.innerHeight, 0.1, 1000);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000000, 0); // 透明背景
            // 高分屏清晰渲染
            renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // 将渲染器添加到页面
            document.body.appendChild(renderer.domElement);

            // 添加光源
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 20, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
        }

        // 创建城门
        function createCityGate() {
            const loader = new THREE.TextureLoader();
            loader.load('img/door.png', function(texture) {
                // 调整纹理设置
                texture.wrapS = THREE.ClampToEdgeWrapping;
                texture.wrapT = THREE.ClampToEdgeWrapping;
                texture.magFilter = THREE.LinearFilter;
                texture.minFilter = THREE.LinearMipMapLinearFilter;

                // 城门尺寸设置
                const gateHeight = 8; // 城门高度
                const gateWidth = 20; // 城门宽度设为20米

                const gateGeometry = new THREE.PlaneGeometry(gateWidth, gateHeight);
                const gateMaterial = new THREE.MeshLambertMaterial({
                    map: texture,
                    transparent: true,
                    side: THREE.DoubleSide,
                    opacity: 1
                });

                const gateMesh = new THREE.Mesh(gateGeometry, gateMaterial);

                // 位置设置：横跨道路，在100米处（-100的Z位置，因为玩家向-Z前进）
                gateMesh.position.set(0, gateHeight/2, -100); // 100米处
                gateMesh.rotation.y = 0; // 面向玩家前进方向

                // 添加到场景
                scene.add(gateMesh);
                
                // 存储城门引用以便后续管理
                window.cityGate = gateMesh;

            }, undefined, function(error) {
                console.warn('城门图片加载失败:', error);
            });
        }

        // 创建左侧街道（根据图片实际宽高比动态设置平面尺寸）
        function createLeftBackground() {
            const loader = new THREE.TextureLoader();
            loader.load('img/left.png', function(texture) {
                // 调整纹理设置以优化显示效果
                texture.wrapS = THREE.RepeatWrapping;
                texture.wrapT = THREE.RepeatWrapping;
                texture.magFilter = THREE.LinearFilter;
                texture.minFilter = THREE.LinearMipMapLinearFilter;

                // 基于图片宽高比动态计算平面尺寸
                const img = texture.image;
                const aspect = img && img.height ? (img.width / img.height) : 1; // w/h
                const planeHeight = 3; // 街道高度设为3米
                const planeWidth = planeHeight * aspect; // 保持不变形

                const backgroundGeometry = new THREE.PlaneGeometry(planeWidth, planeHeight);
                const backgroundMaterial = new THREE.MeshBasicMaterial({
                    map: texture,
                    transparent: true,
                    side: THREE.DoubleSide,
                    opacity: 1
                });

                const backgroundMesh = new THREE.Mesh(backgroundGeometry, backgroundMaterial);
                backgroundMesh.frustumCulled = false;

                // 位置与角度 - 调整Y位置以适应3米高度，X位置适应2米宽赛道
                const yPos = planeHeight / 2; // 底部贴地，顶部3米高
                backgroundMesh.position.set(-1, yPos, 0); // 紧贴2米宽赛道左侧
                backgroundMesh.rotation.y = Math.PI / 2; // 面向道路

                scene.add(backgroundMesh);

                // 沿Z方向的拼接间距=平面宽度（在默认Euler顺序XYZ下，先绕X小角度再绕Y 90°，宽度轴仍映射到Z，不需投影校正）
                const spacing = planeWidth;

                // 创建连续的背景墙效果，按间距无缝拼接
                const backgroundInstances = [];
                for (let i = -8; i <= 8; i++) {
                    if (i === 0) {
                        backgroundInstances.push(backgroundMesh);
                        continue;
                    }
                    const bgClone = backgroundMesh.clone();
                    bgClone.position.z = i * spacing;
                    scene.add(bgClone);
                    backgroundInstances.push(bgClone);
                }

                // 存储背景实例与间距，以便后续动态管理
                window.leftBackgroundInstances = backgroundInstances;
                window.leftBackgroundSpacing = spacing;

            }, undefined, function(error) {
                console.warn('背景图片加载失败:', error);
            });
        }

        // 动态更新左侧街道（循环复用，真正无限滚动）
        function updateLeftBackground() {
            const instances = window.leftBackgroundInstances;
            if (!instances || instances.length === 0) return;

            const spacing = window.leftBackgroundSpacing || (instances[0]?.geometry?.parameters?.width) || 60;

            // 找到最前端(最小z)与最后端(最大z)的面板
            function scanMinMax() {
                let minZ = Infinity, maxZ = -Infinity, minIdx = -1, maxIdx = -1;
                for (let i = 0; i < instances.length; i++) {
                    const z = instances[i].position.z;
                    if (z < minZ) { minZ = z; minIdx = i; }
                    if (z > maxZ) { maxZ = z; maxIdx = i; }
                }
                return { minZ, maxZ, minIdx, maxIdx };
            }

            let { minZ, maxZ, minIdx, maxIdx } = scanMinMax();

            // 目标覆盖范围：
            // 前方（玩家前进方向 -Z）至少覆盖8段；身后保留2段缓冲
            const targetFrontMin = playerZ - spacing * 8; // 需要最小z <= 该值
            const targetBackMax = playerZ + spacing * 2;  // 需要最大z >= 该值

            // 如果前方覆盖不足，把最后端面板搬到最前端，直到满足
            while (minZ > targetFrontMin) {
                const panel = instances[maxIdx];
                panel.position.z = minZ - spacing;
                ({ minZ, maxZ, minIdx, maxIdx } = scanMinMax());
            }

            // 如果身后覆盖不足，把最前端面板搬到最后端，直到满足
            while (maxZ < targetBackMax) {
                const panel = instances[minIdx];
                panel.position.z = maxZ + spacing;
                ({ minZ, maxZ, minIdx, maxIdx } = scanMinMax());
            }
        }

        // 创建跑道
        function createRoad() {
            const roadGroup = new THREE.Group();

            // 跑道参数
            const roadLength = 500;
            const roadWidth = 2; // 赛道宽度设为2米
            const laneWidth = roadWidth / 3;

            // 创建多段跑道以实现无限效果
            for (let i = -2; i <= 2; i++) {
                const segmentGroup = new THREE.Group();
                segmentGroup.position.z = i * roadLength;

                // 黄色跑道主体
                const roadGeometry = new THREE.PlaneGeometry(roadWidth, roadLength);
                const roadMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 });
                const roadMesh = new THREE.Mesh(roadGeometry, roadMaterial);
                roadMesh.rotation.x = -Math.PI / 2;
                roadMesh.position.y = 0;
                roadMesh.receiveShadow = true;
                segmentGroup.add(roadMesh);

                // 白色分割线（将跑道等分成三段）
                const lineGeometry = new THREE.BoxGeometry(0.3, 0.05, roadLength);
                const lineMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });

                // 计算正确的分割线位置，确保三个车道等宽
                // 跑道总宽12，分成3段，每段宽4
                // 分割线位置：-6, -2, +2, +6 (边界和分割)

                // 左边界线
                const leftBorder = new THREE.Mesh(lineGeometry, lineMaterial);
                leftBorder.position.set(-roadWidth/2, 0.025, 0); // -6
                segmentGroup.add(leftBorder);

                // 第一条分割线（左车道和中车道之间）
                const leftDivider = new THREE.Mesh(lineGeometry, lineMaterial);
                leftDivider.position.set(-roadWidth/6, 0.025, 0); // -2
                segmentGroup.add(leftDivider);

                // 第二条分割线（中车道和右车道之间）
                const rightDivider = new THREE.Mesh(lineGeometry, lineMaterial);
                rightDivider.position.set(roadWidth/6, 0.025, 0); // +2
                segmentGroup.add(rightDivider);

                // 右边界线
                const rightBorder = new THREE.Mesh(lineGeometry, lineMaterial);
                rightBorder.position.set(roadWidth/2, 0.025, 0); // +6
                segmentGroup.add(rightBorder);

                // 绿色空地（左右两侧）
                const grassWidth = 50; // 增加绿地宽度以覆盖更大范围
                const grassGeometry = new THREE.PlaneGeometry(grassWidth, roadLength);
                const grassMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });

                // 左侧绿地
                const leftGrass = new THREE.Mesh(grassGeometry, grassMaterial);
                leftGrass.rotation.x = -Math.PI / 2;
                leftGrass.position.set(-roadWidth/2 - grassWidth/2, -0.01, 0);
                leftGrass.receiveShadow = true;
                segmentGroup.add(leftGrass);

                // 右侧绿地
                const rightGrass = new THREE.Mesh(grassGeometry, grassMaterial);
                rightGrass.rotation.x = -Math.PI / 2;
                rightGrass.position.set(roadWidth/2 + grassWidth/2, -0.01, 0);
                rightGrass.receiveShadow = true;
                segmentGroup.add(rightGrass);

                roadGroup.add(segmentGroup);
            }

            scene.add(roadGroup);
            road = roadGroup;
        }

        // 创建玩家
        function createPlayer() {
            const playerGroup = new THREE.Group();

            // 使用图片纹理创建玩家
            const loader = new THREE.TextureLoader();
            loader.load('img/player.png', function(texture) {
                // 调整纹理设置
                texture.wrapS = THREE.ClampToEdgeWrapping;
                texture.wrapT = THREE.ClampToEdgeWrapping;
                texture.magFilter = THREE.LinearFilter;
                texture.minFilter = THREE.LinearMipMapLinearFilter;

                // 基于图片宽高比动态计算玩家尺寸
                const img = texture.image;
                const aspect = img && img.height ? (img.width / img.height) : 1; // w/h
                const playerHeight = 2; // 人物高度设为2米
                const playerWidth = playerHeight * aspect; // 保持比例不变形

                const playerGeometry = new THREE.PlaneGeometry(playerWidth, playerHeight);
                const playerMaterial = new THREE.MeshLambertMaterial({
                    map: texture,
                    transparent: true,
                    side: THREE.DoubleSide,
                    opacity: 1
                });

                const playerMesh = new THREE.Mesh(playerGeometry, playerMaterial);
                playerMesh.position.y = playerHeight / 2; // 底部贴地，顶部2米高
                playerMesh.castShadow = true;

                // 清除之前的子对象（如果有的话）
                while(playerGroup.children.length > 0) {
                    playerGroup.remove(playerGroup.children[0]);
                }

                playerGroup.add(playerMesh);

                // 存储玩家网格引用以便后续使用
                window.playerMesh = playerMesh;

            }, undefined, function(error) {
                console.warn('玩家图片加载失败，使用默认模型:', error);
                // 如果图片加载失败，使用原来的3D模型作为备用
                createDefaultPlayer();
            });

            // 设置初始位置
            playerGroup.position.set(0, 0, playerZ);
            // 前进方向为 -Z，人物需转向面朝 -Z
            playerGroup.rotation.y = Math.PI;

            scene.add(playerGroup);
            player = playerGroup;
        }

        // 备用的默认3D玩家模型
        function createDefaultPlayer() {
            // 身体
            const bodyGeometry = new THREE.BoxGeometry(1, 2, 0.5);
            const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0xFF6B6B });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.y = 1;
            body.castShadow = true;
            player.add(body);

            // 头部
            const headGeometry = new THREE.SphereGeometry(0.4, 8, 8);
            const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFFE4B5 });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.y = 2.4;
            head.castShadow = true;
            player.add(head);

            // 眼睛
            const eyeGeometry = new THREE.SphereGeometry(0.1, 4, 4);
            const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });

            const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            leftEye.position.set(-0.15, 2.5, 0.3);
            player.add(leftEye);

            const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            rightEye.position.set(0.15, 2.5, 0.3);
            player.add(rightEye);
        }

        // 获取车道X坐标
        function getLaneX(lane) {
            const roadWidth = 2; // 赛道宽度2米
            const laneWidth = roadWidth / 3; // 每个车道宽度 = 0.67米
            // 车道中心位置计算：
            // 左车道中心：-1 + 0.33 = -0.67
            // 中车道中心：-0.33 + 0.33 = 0
            // 右车道中心：+0.33 + 0.33 = +0.67
            return -roadWidth/2 + laneWidth/2 + lane * laneWidth; // lane 0,1,2 -> -0.67,0,0.67
        }

        // 创建金币
        function createCoin(lane, z) {
            const coinGroup = new THREE.Group();

            const coin = new THREE.Mesh(shared.coinGeometry, shared.coinMaterial);
            coin.castShadow = false;
            coinGroup.add(coin);

            coinGroup.position.set(getLaneX(lane), 1, z);
            coinGroup.userData = { type: 'coin', lane: lane };

            scene.add(coinGroup);
            gameObjects.push(coinGroup);

            return coinGroup;
        }

        // 创建障碍物
        function createObstacle(lane, z) {
            const obstacleGroup = new THREE.Group();

            const obstacle = new THREE.Mesh(shared.obstacleGeometry, shared.obstacleMaterial);
            obstacle.position.y = 0.75;
            obstacle.castShadow = true;
            obstacleGroup.add(obstacle);

            obstacleGroup.position.set(getLaneX(lane), 0, z);
            obstacleGroup.userData = { type: 'obstacle', lane: lane };

            scene.add(obstacleGroup);
            gameObjects.push(obstacleGroup);

            return obstacleGroup;
        }

        // 生成游戏对象
        function spawnObjects() {
            // 在更远的地方生成物体，避免突兀出现
            const spawnZ = playerZ - 200; // 反向前进：在玩家前方更小Z生成

            // 节流：每前进一定距离才尝试生成，避免每帧写入
            if (Math.abs(spawnZ - lastSpawnZ) >= 2) {
                lastSpawnZ = spawnZ;

                // 生成金币（受最小间距与上限控制）
                if (gameObjects.filter(o => o.userData.type === 'coin').length < MAX_COINS) {
                    if (Math.random() < 0.15 && Math.abs(spawnZ - lastCoinZ) >= baseCoinGap) {
                        const lane = Math.floor(Math.random() * 3);
                        createCoin(lane, spawnZ);
                        lastCoinZ = spawnZ;
                    }
                }

                // 生成障碍物（受难度、最小间距与上限控制）
                if (gameObjects.filter(o => o.userData.type === 'obstacle').length < MAX_OBSTACLES) {
                    const effectiveObstacleProb = Math.min(1, obstacleSpawnBaseProb * difficulty);
                    const minGap = baseObstacleGap / Math.max(0.2, difficulty); // 难度高 -> 间距更小
                    if (Math.random() < effectiveObstacleProb && Math.abs(spawnZ - lastObstacleZ) >= minGap) {
                        const lane = Math.floor(Math.random() * 3);
                        createObstacle(lane, spawnZ);
                        lastObstacleZ = spawnZ;
                    }
                }
            }
        }

        // 输入处理
        document.addEventListener('keydown', (e) => {
            keys[e.key] = true;

            if (gameState === 'playing') {
                if ((e.key === 'ArrowLeft' || e.key === 'a' || e.key === 'A') && targetLane > 0) {
                    targetLane--;
                    if (DEBUG_LOG) console.log('向左移动到车道:', targetLane);
                }
                if ((e.key === 'ArrowRight' || e.key === 'd' || e.key === 'D') && targetLane < 2) {
                    targetLane++;
                    if (DEBUG_LOG) console.log('向右移动到车道:', targetLane);
                }
            }

            // 键盘不再用空格重开
        });

        document.addEventListener('keyup', (e) => {
            keys[e.key] = false;
        });

        // 触摸控制
        let touchStartX = 0;
        document.addEventListener('touchstart', (e) => {
            if (gameState === 'gameOver') {
                restartGame();
                return;
            }
            touchStartX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', (e) => {
            if (gameState !== 'playing') return;

            const touchEndX = e.changedTouches[0].clientX;
            const diff = touchEndX - touchStartX;

            if (Math.abs(diff) > 50) {
                if (diff < 0 && targetLane > 0) {
                    // 左滑 -> 向左移动
                    targetLane--;
                    if (DEBUG_LOG) console.log('左滑，向左移动到车道:', targetLane);
                } else if (diff > 0 && targetLane < 2) {
                    // 右滑 -> 向右移动
                    targetLane++;
                    if (DEBUG_LOG) console.log('右滑，向右移动到车道:', targetLane);
                }
            }
        });

        // 点击重开（方便PC端测试）
        document.addEventListener('click', () => {
            if (gameState === 'gameOver') {
                restartGame();
            }
        });

        // 碰撞检测
        function checkCollisions() {
            for (let i = gameObjects.length - 1; i >= 0; i--) {
                const obj = gameObjects[i];
                const distance = Math.abs(obj.position.z - player.position.z);

                if (distance < 2) {
                    if (obj.userData.type === 'coin') {
                        const laneDistance = Math.abs(obj.position.x - player.position.x);
                        if (laneDistance < 1.5) {
                            // 收集金币
                            scene.remove(obj);
                            gameObjects.splice(i, 1);
                            score += 10;
                            document.getElementById('coins').textContent = score;
                        }
                    } else if (obj.userData.type === 'obstacle') {
                        const laneDistance = Math.abs(obj.position.x - player.position.x);
                        if (laneDistance < 1.5) {
                            // 碰到障碍物
                            gameState = 'gameOver';
                            showGameOver();
                            return;
                        }
                    }
                }
            }
        }

        // 更新摄像机位置（优化的跟随视角）
        function updateCamera() {
            // 摄像机在玩家后方上方位置，适应2米宽赛道、2米高玩家和3米高街道
            const distance = 6; // 适当调整距离以适应更窄的赛道
            const height = 3;   // 调整高度以获得更好的视角

            // 摄像机位置：X固定为0（中间跑道），Y适应玩家高度，Z位于玩家后方
            camera.position.set(0, player.position.y + height, player.position.z + distance);

            // 摄像机看向玩家前方一定距离，提供更好的前瞻视野
            tempLookAt.set(0, player.position.y + 1, player.position.z - 8);
            camera.lookAt(tempLookAt);
        }

        // 固定步长的游戏逻辑更新
        function gameStep(dt) {
            if (gameState !== 'playing') return;

            // 玩家前进（按秒计，反向：-Z）
            playerZ -= playerSpeed * dt;
            player.position.z = playerZ;

            // 平滑移动到目标车道
            const targetX = getLaneX(targetLane);
            const oldX = player.position.x;
            player.position.x += (targetX - player.position.x) * 0.15;

            if (DEBUG_LOG && Math.abs(targetX - oldX) > 0.1) {
                console.log(`目标车道: ${targetLane}, 目标X: ${targetX.toFixed(2)}, 当前X: ${player.position.x.toFixed(2)}`);
            }

            // 更新距离
            distance = Math.floor(playerZ / 2);
            document.getElementById('distance').textContent = distance;

            // 每帧检查并生成需要的对象
            spawnObjects();

            // 移除远离的对象（可碰撞对象）
            for (let i = gameObjects.length - 1; i >= 0; i--) {
                const obj = gameObjects[i];
                if (obj.position.z > playerZ + 50) {
                    scene.remove(obj);
                    gameObjects.splice(i, 1);
                    continue;
                }
                // 极远的前方也做限制，避免累计
                if (obj.position.z < playerZ - 400) {
                    scene.remove(obj);
                    gameObjects.splice(i, 1);
                }
            }
            // 移除远离的对象（风景对象）
            for (let i = sceneryObjects.length - 1; i >= 0; i--) {
                const obj = sceneryObjects[i];
                if (obj.position.z > playerZ + 80) {
                    scene.remove(obj);
                    sceneryObjects.splice(i, 1);
                    continue;
                }
                if (obj.position.z < playerZ - 500) {
                    scene.remove(obj);
                    sceneryObjects.splice(i, 1);
                }
            }

            // 更新跑道位置（无限跑道效果）
            if (road) {
                // 让跑道跟随玩家移动（反向同样按Z取整）
                road.position.z = Math.floor(playerZ / 500) * 500;
            }

            // 更新左侧街道位置（动态生成新的背景实例）
            updateLeftBackground();

            // 检查碰撞
            checkCollisions();

            // 更新摄像机
            updateCamera();
        }

        // 显示倒计时
        function showCountdown() {
            document.getElementById('countdown').style.display = 'block';
            document.getElementById('countdown').textContent = '3';
            clock.elapsedTime = 0;
            clock.start();
        }

        // 显示游戏结束
        function showGameOver() {
            document.getElementById('finalScore').textContent = score;
            document.getElementById('finalDistance').textContent = distance;
            document.getElementById('gameOver').style.display = 'block';
        }

        // 重新开始游戏
        function restartGame() {
            gameState = 'countdown';
            countdownTimer = 3;
            score = 0;
            distance = 0;
            playerLane = 1;
            targetLane = 1;
            playerZ = 0;
            lastObstacleZ = Infinity;
            lastHouseZ = -200;
            lastTreeZ = -200;
            accumulator = 0;

            // 重置玩家位置
            player.position.set(0, 0, 0);

            // 清除所有游戏对象
            for (let obj of gameObjects) {
                scene.remove(obj);
            }
            gameObjects = [];
            for (let obj of sceneryObjects) {
                scene.remove(obj);
            }
            sceneryObjects = [];

            // 重置UI
            document.getElementById('coins').textContent = '0';
            document.getElementById('distance').textContent = '0';
            document.getElementById('gameOver').style.display = 'none';

            showCountdown();
            clock.elapsedTime = 0;
            clock.start();
        }

        // 渲染循环
        function animate() {
            const delta = clock.getDelta();

            // 倒计时用真实时间推进
            if (gameState === 'countdown') {
                countdownTimer -= delta;
                document.getElementById('countdown').textContent = Math.ceil(Math.max(0, countdownTimer));
                // 在倒计时阶段保持与正式开始一致的摄像机视角
                updateCamera();
                if (countdownTimer <= 0) {
                    gameState = 'playing';
                    document.getElementById('countdown').style.display = 'none';
                }
            }

            // 固定步长推进游戏逻辑
            if (gameState === 'playing') {
                accumulator += Math.min(delta, 0.1);
                while (accumulator >= FIXED_DT) {
                    gameStep(FIXED_DT);
                    accumulator -= FIXED_DT;
                }
            }

            // FPS 计算：每0.5秒更新一次显示
            if (!window.__fpsSamples) {
                window.__fpsSamples = { sum: 0, count: 0, time: 0 };
            }
            const s = window.__fpsSamples;
            s.sum += (1 / Math.max(0.000001, delta));
            s.count += 1;
            s.time += delta;
            if (s.time >= 0.5) {
                const avgFps = Math.round(s.sum / s.count);
                const el = document.getElementById('fps');
                if (el) el.textContent = String(avgFps);
                s.sum = 0;
                s.count = 0;
                s.time = 0;
            }

            renderer.render(scene, camera);
            requestAnimationFrame(animate);
        }

        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
        });

        // 初始化游戏
        function initGame() {
            initThreeJS();
            createLeftBackground(); // 创建左侧街道
            createCityGate(); // 创建城门
            createRoad();
            createPlayer();

            // 设置初始摄像机位置（与 updateCamera 逻辑一致）
            camera.position.set(0, 4, 8);
            camera.lookAt(0, 1, -6);

            // 难度滑块事件
            const slider = document.getElementById('difficultySlider');
            const valueEl = document.getElementById('difficultyValue');
            valueEl.textContent = slider.value;
            difficulty = parseFloat(slider.value);
            slider.addEventListener('input', () => {
                difficulty = parseFloat(slider.value);
                valueEl.textContent = slider.value;
            });

            showCountdown();
            animate();
        }

        // 启动游戏
        initGame();
    </script>
</body>
</html>